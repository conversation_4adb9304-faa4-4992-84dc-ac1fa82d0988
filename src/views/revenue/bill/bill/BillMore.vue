<template>
  <div>
    <qey-drawer :visible.sync="open" title="账单详情">
      <qey-more-top id="moreShow" :desc-arr="descArr">
        <template #first>{{ form.billNum }}</template>
        <template #second>
          <!-- 审核状态-->
          <dict-tag :options="dict.type['sw_exam_state']" :value="form.examState" size="mini"/>
          <!-- 账单状态 -->
          <dict-tag :options="dict.type['sw_bill_status']" :value="form.billStatus" size="mini"/>
        </template>
      </qey-more-top>
      <el-tabs :active-name="activeName" @tab-click="handleClick">
        <el-tab-pane label="账单信息" name="info">
          <bill-info :dict-data="dictData" :form="form" :style="tabStyle"></bill-info>
        </el-tab-pane>
        <el-tab-pane label="账单明细" name="detail">
          <bill-detail :dict-data="dictData" :form="form" :style="tabStyle"></bill-detail>
        </el-tab-pane>
        <el-tab-pane label="应收单" name="receivable">
          <receivable-list ref="receivable" :style="tabStyle"></receivable-list>
        </el-tab-pane>
        <el-tab-pane label="发票" name="invoice">
          <application-detail :dict-data="dictData" :form="invoiceData" :style="tabStyle"></application-detail>
        </el-tab-pane>
        <el-tab-pane label="销账明细" name="writeOff">

        </el-tab-pane>
      </el-tabs>
    </qey-drawer>
  </div>

</template>

<script>
import {getInfo} from "@/api/revenue/bill/bill";
import BillInfo from "@/views/revenue/bill/bill/more_component/BillInfo.vue";
import BillDetail from "@/views/revenue/bill/bill/more_component/BillDetail.vue";
import ReceivableList from "@/views/revenue/bill/bill/more_component/ReceivableList.vue";
import ApplicationDetail from "@/views/revenue/bill/bill/more_component/ApplicationDetail.vue";
import {getBillDetail1} from "@/api/revenue/control/application";

export default {
  name: "BillMore",
  dicts: ["sw_bill_status", "sw_exam_state"],
  components: {BillInfo, BillDetail, ReceivableList, ApplicationDetail},
  data() {
    return {
      title: '账单详情',
      open: false,
      form: {},
      activeName: 'info',
      receivableData: [],
      invoiceData: []
    }
  },
  computed: {
    dictData: {
      get() {
        return this.dict.type
      }
    },
    descArr() {
      return [
        {
          title: '户主姓名',
          description: this.form.userName
        },
        {
          title: '欠费金额',
          description: this.form.arrearsAmount
        },
        {
          title: '开账时间',
          description: this.form.billTime || '暂无'
        },
      ];
    }
  },
  methods: {
    handleMore(params) {

      this.$nextTick(async () => {
        this.activeName = 'info'
        this.form = {}
        this.receivableData = []
        this.invoiceData = []
        const {code, data} = await getInfo(params)
        this.form = data
        await getBillDetail1(params).then(res => {
          this.invoiceData = res.data
        })
        console.log(this.form);
        this.open = true
      })

    },
    handleClick(tab, event) {
      const map = {
        'info': () => {
        },
        'receivable': () => {
          this.$refs.receivable.handleReceivable(this.form.billNum)
        },
        'invoice': () => {
          this.$refs.applicationDetail.handleInvoice(this.form.billNum)
        }
      }
      map[tab.name] ? map[tab.name]() : ''
    }
  },
}

</script>
