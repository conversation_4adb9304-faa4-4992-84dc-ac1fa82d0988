<template>

  <div>
    <el-table :data="receivableData" border size="mini">
      <el-table-column align="center" label="应收单号" prop="orderNum" show-overflow-tooltip width="130"/>
      <el-table-column align="center" label="申请时间" prop="receivableDate" show-overflow-tooltip/>
      <el-table-column align="center" label="类型" prop="orderType">
        <template slot-scope="scope">
                    <span v-for="item in orderData" :key="item.value">
                        <span v-if="item.value == scope.row.orderType">{{ item.label }}</span>
                    </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="户号" prop="userAccount" show-overflow-tooltip width="140"/>
      <el-table-column align="center" label="户主" prop="userName" show-overflow-tooltip width="200"/>
      <el-table-column align="center" label="总金额" prop="totalAmount" show-overflow-tooltip/>
      <el-table-column align="center" label="审核状态" prop="examState">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_receivable_status" :value="scope.row.examState"/>
        </template>
      </el-table-column>

    </el-table>

  </div>
</template>
<script>


import {getReceivableList} from "@/api/revenue/receivable/account";

export default {
  name: "ReceivableList",
  dicts: [
    "sys_receivable_status",
  ],
  data() {
    return {
      receivableData: [],
      open: false,
      orderData: [
        {
          label: '日常应收单',
          value: '1'
        },
        {
          label: '红冲应收单',
          value: '2'
        }
      ],
    }
  },


  methods: {
    async handleReceivable(billNum) {
      const {data} = await getReceivableList(billNum)
      this.receivableData = data
    }
  }
}
</script>


<style lang="scss" scoped></style>
