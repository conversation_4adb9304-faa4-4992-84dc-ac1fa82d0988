<template>

  <div>
    <el-table :data="invoiceData" border size="mini">
      <el-table-column align="center" label="发票号" prop="invoiceNum" show-overflow-tooltip width="130"/>
      <el-table-column align="center" label="发票类型" prop="invoiceType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sw_invoice_type" :value="scope.row.invoiceType"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="开票总金额" prop="invoiceAmount" show-overflow-tooltip/>
      <el-table-column align="center" label="发票状态" prop="invoiceState">
        <template slot-scope="scope">
          <span v-for="item in invoiceStateData" :key="item.value">
            <span v-if="item.value == scope.row.invoiceState">{{ item.label }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="开票时间" prop="makeTime" show-overflow-tooltip width="160"/>
      <el-table-column align="center" label="发票" prop="invoiceUrl" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button v-if="scope.row.invoiceName" size="mini" type="text"
                     @click="handlePreview(scope.row.invoiceUrl)">
            {{ scope.row.invoiceName }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <qey-preview ref="qeyPreview"></qey-preview>

  </div>
</template>
<script>

import {getBillDetail1} from "@/api/revenue/control/application";

export default {
  name: "ApplicationDetail",
  dicts: [
    "sw_invoice_type",
  ],
  data() {
    return {
      invoiceData: [],
      open: false,
      invoiceStateData: [
        {
          label: '未开票',
          value: '0'
        },
        {
          label: '已开票',
          value: '1'
        }
      ],
    }
  },

  methods: {
    async handleInvoice(billNum) {
      const {data} = await getBillDetail1({billNum})
      this.invoiceData = data
    },
    handlePreview(url) {
      this.$refs.qeyPreview.handlePreview(url);
    },
  }
}
</script>


<style lang="scss" scoped></style>