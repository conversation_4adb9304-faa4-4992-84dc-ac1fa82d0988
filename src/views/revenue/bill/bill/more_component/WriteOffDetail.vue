<template>

  <div>
    <el-table :data="writeOffData" border size="mini">
      <el-table-column align="center" label="销账单号" prop="writeOffNum" show-overflow-tooltip width="130"/>
      <el-table-column align="center" label="销账时间" prop="writeOffDate" show-overflow-tooltip width="160"/>
      <el-table-column align="center" label="销账类型" prop="writeOffType">
        <template slot-scope="scope">
          <span v-for="item in writeOffTypeData" :key="item.value">
            <span v-if="item.value == scope.row.writeOffType">{{ item.label }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="销账金额" prop="writeOffAmount" show-overflow-tooltip/>
      <el-table-column align="center" label="销账方式" prop="writeOffMethod">
        <template slot-scope="scope">
          <span v-for="item in writeOffMethodData" :key="item.value">
            <span v-if="item.value == scope.row.writeOffMethod">{{ item.label }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作人" prop="operatorName" show-overflow-tooltip width="100"/>
      <el-table-column align="center" label="销账状态" prop="writeOffStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sw_writeoff_status" :value="scope.row.writeOffStatus"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark" show-overflow-tooltip/>
    </el-table>

  </div>
</template>
<script>

import {getWriteOffList} from "@/api/revenue/bill/bill";

export default {
  name: "WriteOffDetail",
  dicts: [
    "sw_writeoff_status",
  ],
  data() {
    return {
      writeOffData: [],
      open: false,
      writeOffTypeData: [
        {
          label: '正常销账',
          value: '1'
        },
        {
          label: '坏账销账',
          value: '2'
        },
        {
          label: '减免销账',
          value: '3'
        }
      ],
      writeOffMethodData: [
        {
          label: '现金缴费',
          value: '1'
        },
        {
          label: '银行转账',
          value: '2'
        },
        {
          label: '支付宝',
          value: '3'
        },
        {
          label: '微信支付',
          value: '4'
        },
        {
          label: '其他方式',
          value: '5'
        }
      ],
    }
  },

  methods: {
    async handleWriteOff(billNum) {
      const {data} = await getWriteOffList(billNum)
      this.writeOffData = data
    }
  }
}
</script>


<style lang="scss" scoped></style>
