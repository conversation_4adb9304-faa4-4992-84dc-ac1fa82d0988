import request from '@/utils/request'


// 获取应收单列表
export function getReceivable(query) {
  return request({
    url: '/revenue/receivable/list',
    method: 'get',
    params: query
  })
}

// 获取应收单列表
export function getBillInfo(query) {
  return request({
    url: '/revenue/bill/info/getBillInfo',
    method: 'get',
    params: query
  })
}

// 获取待处理账务列表
export function getOrderInfo(query) {
  return request({
    url: '/revenue/receivable/getOrderInfo',
    method: 'get',
    params: query
  })
}

// 应收单申请保存
export function addReceivable(data) {
  return request({
    url: '/revenue/receivable/add',
    method: 'post',
    data: data
  })
}

// 应收单详情
export function getDetail(orderId) {
  return request({
    url: '/revenue/receivable/getDetail/' + orderId,
    method: 'get'
  })
}

// 应收单详情
export function getBillDetail(billNum) {
  return request({
    url: '/revenue/receivable/getBillDetail/' + billNum,
    method: 'get'
  })
}
