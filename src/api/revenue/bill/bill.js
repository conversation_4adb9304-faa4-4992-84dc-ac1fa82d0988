import request from "@/utils/request";

// 获取账单列表
export function getBillList(params) {
  return request({
    url: "/revenue/bill/info/list",
    method: "get",
    params
  })
}

// 获取账单详情
export function getInfo(params) {
  return request({
    url: "/revenue/bill/info/getInfo",
    method: "get",
    params
  })
}

export function againMake(billId) {
  return request({
    url: "/revenue/bill/info/againMake/" + billId,
    method: "get",
  })
}


export function writeOff(data) {
  return request({
    url: "/revenue/bill/info/writeOff",
    method: "post",
    data
  })
}

// 批量核销账单
export function writeOffBatch(userAccounts) {
  return request({
    url: "/revenue/bill/info/writeOffBatch/" + userAccounts,
    method: "get",
  })
}

// 获取销账明细列表
export function getWriteOffList(billNum) {
  return request({
    url: "/revenue/bill/info/getWriteOffList/" + billNum,
    method: "get"
  })
}

